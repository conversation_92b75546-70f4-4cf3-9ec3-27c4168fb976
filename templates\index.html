<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Website Cloner</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 600px;
            width: 100%;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            color: #666;
            font-size: 1.1em;
        }

        .clone-form {
            margin-bottom: 30px;
        }

        .input-group {
            margin-bottom: 20px;
        }

        .input-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 600;
        }

        .url-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .url-input:focus {
            outline: none;
            border-color: #667eea;
        }

        .clone-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .clone-btn:hover {
            transform: translateY(-2px);
        }

        .clone-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .result {
            display: none;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
        }

        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .download-btn {
            background: #28a745;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-top: 10px;
        }

        .files-section {
            margin-top: 30px;
            padding-top: 30px;
            border-top: 2px solid #e1e5e9;
        }

        .files-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .file-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border: 1px solid #e1e5e9;
            border-radius: 5px;
            margin-bottom: 10px;
            background: #f8f9fa;
        }

        .file-info {
            flex-grow: 1;
        }

        .file-name {
            font-weight: 600;
            color: #333;
        }

        .file-details {
            font-size: 0.9em;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌐 Website Cloner</h1>
            <p>Enter a URL to download and save the webpage HTML</p>
        </div>

        <form class="clone-form" id="cloneForm">
            <div class="input-group">
                <label for="url">Website URL:</label>
                <input 
                    type="text" 
                    id="url" 
                    class="url-input" 
                    placeholder="https://example.com or example.com"
                    required
                >
            </div>
            <button type="submit" class="clone-btn" id="cloneBtn">
                🔄 Clone Website
            </button>
        </form>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>Downloading website...</p>
        </div>

        <div class="result" id="result"></div>

        <div class="files-section">
            <h3>📁 Downloaded Files</h3>
            <div class="files-list" id="filesList">
                <p>Loading files...</p>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('cloneForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const url = document.getElementById('url').value.trim();
            const cloneBtn = document.getElementById('cloneBtn');
            const loading = document.getElementById('loading');
            const result = document.getElementById('result');
            
            if (!url) {
                showResult('Please enter a URL', 'error');
                return;
            }
            
            // Show loading state
            cloneBtn.disabled = true;
            loading.style.display = 'block';
            result.style.display = 'none';
            
            try {
                const response = await fetch('/clone', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ url: url })
                });

                // Check if response is ok
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                // Check if response is JSON
                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    const text = await response.text();
                    throw new Error(`Server returned non-JSON response: ${text.substring(0, 200)}...`);
                }

                const data = await response.json();
                
                if (data.success) {
                    const message = `
                        <strong>✅ Success!</strong><br>
                        Website: ${data.url}<br>
                        File: ${data.filename}<br>
                        Size: ${(data.size / 1024).toFixed(2)} KB<br>
                        <a href="/download/${data.filename}" class="download-btn">📥 Download File</a>
                    `;
                    showResult(message, 'success');
                    loadFiles(); // Refresh file list
                } else {
                    showResult(`❌ Error: ${data.error}`, 'error');
                }
            } catch (error) {
                console.error('Clone error:', error);
                let errorMessage = error.message;

                // Provide more user-friendly error messages
                if (errorMessage.includes('Failed to fetch')) {
                    errorMessage = 'Cannot connect to server. Please check your internet connection.';
                } else if (errorMessage.includes('HTTP error! status: 500')) {
                    errorMessage = 'Server error. The website might be blocking requests or unavailable.';
                } else if (errorMessage.includes('HTTP error! status: 404')) {
                    errorMessage = 'Website not found. Please check the URL.';
                } else if (errorMessage.includes('non-JSON response')) {
                    errorMessage = 'Server error. Please try again in a moment.';
                }

                showResult(`❌ Error: ${errorMessage}`, 'error');
            } finally {
                cloneBtn.disabled = false;
                loading.style.display = 'none';
            }
        });
        
        function showResult(message, type) {
            const result = document.getElementById('result');
            result.innerHTML = message;
            result.className = `result ${type}`;
            result.style.display = 'block';
        }
        
        async function loadFiles() {
            try {
                const response = await fetch('/files');
                const data = await response.json();
                const filesList = document.getElementById('filesList');
                
                if (data.files && data.files.length > 0) {
                    filesList.innerHTML = data.files.map(file => `
                        <div class="file-item">
                            <div class="file-info">
                                <div class="file-name">${file.filename}</div>
                                <div class="file-details">
                                    Size: ${(file.size / 1024).toFixed(2)} KB | 
                                    Modified: ${file.modified}
                                </div>
                            </div>
                            <a href="/download/${file.filename}" class="download-btn">📥 Download</a>
                        </div>
                    `).join('');
                } else {
                    filesList.innerHTML = '<p>No files downloaded yet.</p>';
                }
            } catch (error) {
                document.getElementById('filesList').innerHTML = '<p>Error loading files.</p>';
            }
        }
        
        // Load files on page load
        loadFiles();
    </script>
</body>
</html>
