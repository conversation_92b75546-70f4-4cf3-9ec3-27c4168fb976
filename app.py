from flask import Flask, render_template, request, jsonify, send_file
import requests
import os
from datetime import datetime
from urllib.parse import urlparse
import re
import zipfile
import tempfile

app = Flask(__name__)

# Create downloads directory if it doesn't exist
DOWNLOADS_DIR = 'downloads'
if not os.path.exists(DOWNLOADS_DIR):
    os.makedirs(DOWNLOADS_DIR)

def sanitize_filename(url):
    """Create a safe filename from URL"""
    parsed = urlparse(url)
    domain = parsed.netloc or 'unknown'
    # Remove invalid characters for filename
    domain = re.sub(r'[<>:"/\\|?*]', '_', domain)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    return f"{domain}_{timestamp}.html"

def download_webpage(url):
    """Download webpage content"""
    try:
        # Add protocol if missing
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url

        # Set headers to mimic a real browser
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (<PERSON>HTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }

        # Make request with timeout and allow redirects
        response = requests.get(url, headers=headers, timeout=30, allow_redirects=True, verify=False)
        response.raise_for_status()

        return response.text, response.status_code

    except requests.exceptions.Timeout:
        return None, "Request timed out - website took too long to respond"
    except requests.exceptions.ConnectionError:
        return None, "Connection error - could not reach the website"
    except requests.exceptions.HTTPError as e:
        return None, f"HTTP error {e.response.status_code} - {e.response.reason}"
    except requests.exceptions.RequestException as e:
        return None, f"Request failed: {str(e)}"
    except Exception as e:
        return None, f"Unexpected error: {str(e)}"

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/clone', methods=['POST'])
def clone_website():
    try:
        # Handle both JSON and form data
        if request.is_json:
            data = request.get_json()
            url = data.get('url', '').strip() if data else ''
        else:
            url = request.form.get('url', '').strip()

        if not url:
            return jsonify({'success': False, 'error': 'Please provide a URL'})

        print(f"Attempting to clone: {url}")  # Debug log

        # Download the webpage
        html_content, status = download_webpage(url)

        if html_content is None:
            print(f"Download failed: {status}")  # Debug log
            return jsonify({'success': False, 'error': f'Failed to download: {status}'})

        # Create filename
        filename = sanitize_filename(url)
        filepath = os.path.join(DOWNLOADS_DIR, filename)

        # Save the HTML content
        with open(filepath, 'w', encoding='utf-8', errors='ignore') as f:
            f.write(html_content)

        print(f"Successfully saved: {filename}")  # Debug log

        return jsonify({
            'success': True,
            'message': f'Website cloned successfully!',
            'filename': filename,
            'size': len(html_content),
            'url': url
        })

    except Exception as e:
        print(f"Error in clone_website: {str(e)}")  # Debug log
        return jsonify({'success': False, 'error': f'Server error: {str(e)}'})

@app.route('/download/<filename>')
def download_file(filename):
    """Download the cloned file"""
    try:
        filepath = os.path.join(DOWNLOADS_DIR, filename)
        if os.path.exists(filepath):
            return send_file(filepath, as_attachment=True)
        else:
            return jsonify({'error': 'File not found'}), 404
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/files')
def list_files():
    """List all downloaded files"""
    try:
        files = []
        for filename in os.listdir(DOWNLOADS_DIR):
            if filename.endswith('.html'):
                filepath = os.path.join(DOWNLOADS_DIR, filename)
                size = os.path.getsize(filepath)
                modified = datetime.fromtimestamp(os.path.getmtime(filepath))
                files.append({
                    'filename': filename,
                    'size': size,
                    'modified': modified.strftime('%Y-%m-%d %H:%M:%S')
                })
        
        # Sort by modification time (newest first)
        files.sort(key=lambda x: x['modified'], reverse=True)
        return jsonify({'files': files})
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    print("Website Cloner is starting...")
    # Use environment port for deployment, fallback to 5000 for local
    port = int(os.environ.get('PORT', 5000))
    print(f"Open your browser and go to: http://localhost:{port}")
    app.run(debug=False, host='0.0.0.0', port=port)
