# 🚀 GitHub Upload & Kostenlos Hosten - Deutsche Anleitung

## 📤 **Schritt 1: Code auf GitHub hochladen**

### 1.1 Git initialisieren (im Terminal/PowerShell):
```bash
# In deinem Projektordner
git init
git add .
git commit -m "Website Cloner - Erste Version"
```

### 1.2 GitHub Repository erstellen:
1. <PERSON><PERSON>e zu [github.com](https://github.com)
2. Klicke oben rechts auf **"+"** → **"New repository"**
3. **Repository Name**: `website-cloner` (oder was du willst)
4. **Public** auswählen (kostenlos)
5. **NICHT** "Add README" ankreuzen (haben wir schon)
6. Klicke **"Create repository"**

### 1.3 Code hochladen:
```bash
# Ersetze "DEIN-USERNAME" mit deinem GitHub Namen
git remote add origin https://github.com/DEIN-USERNAME/website-cloner.git
git branch -M main
git push -u origin main
```

**✅ Fertig! Dein Code ist jetzt auf GitHub!**

---

## 🌐 **Schritt 2: Kostenlos hosten**

### **Option A: Railway (Empfohlen - Super einfach)**

1. **Gehe zu [railway.app](https://railway.app)**
2. **"Start a New Project"** klicken
3. **"Deploy from GitHub repo"** wählen
4. **Dein Repository auswählen**
5. **Deploy** klicken
6. **Fertig!** Du bekommst eine URL wie `https://dein-projekt.up.railway.app`

### **Option B: Heroku**

1. **Account erstellen**: [heroku.com](https://heroku.com)
2. **Heroku CLI installieren**
3. **Terminal/PowerShell:**
   ```bash
   heroku login
   heroku create dein-website-cloner
   git push heroku main
   ```
4. **Fertig!** URL: `https://dein-website-cloner.herokuapp.com`

### **Option C: Render**

1. **Account erstellen**: [render.com](https://render.com)
2. **"New Web Service"** klicken
3. **GitHub Repository verbinden**
4. **Settings:**
   - Build Command: `pip install -r requirements.txt`
   - Start Command: `python app.py`
5. **"Create Web Service"** klicken

---

## 🔧 **Wenn du Probleme hast:**

### Git nicht installiert?
- **Windows**: [git-scm.com](https://git-scm.com) herunterladen
- **Mac**: `brew install git`
- **Linux**: `sudo apt install git`

### GitHub Account?
- Kostenlos registrieren auf [github.com](https://github.com)

### Terminal/PowerShell öffnen?
- **Windows**: `Win + R` → `powershell` → Enter
- **Mac**: `Cmd + Space` → "Terminal"
- **Linux**: `Ctrl + Alt + T`

---

## 📱 **Was passiert dann?**

1. **GitHub**: Dein Code ist öffentlich sichtbar (Portfolio!)
2. **Hosting**: Deine App läuft 24/7 im Internet
3. **URL**: Du bekommst eine echte Website-Adresse
4. **Updates**: Einfach `git push` für Updates

---

## 💡 **Pro-Tipps:**

- **Repository Name**: Wähle einen guten Namen (z.B. `website-cloner`)
- **README**: Deine README.md wird automatisch auf GitHub angezeigt
- **Updates**: Änderungen mit `git add .`, `git commit -m "Update"`, `git push`
- **Kostenlos**: Alle genannten Services haben kostenlose Tiers

---

## 🆘 **Hilfe benötigt?**

**Häufige Fehler:**
- `git: command not found` → Git installieren
- `Permission denied` → GitHub SSH Key einrichten
- `App crashed` → Logs in der Hosting-Platform checken

**GitHub Repository Beispiel:**
`https://github.com/dein-username/website-cloner`

**Live App Beispiel:**
`https://dein-website-cloner.up.railway.app`

---

## 🎉 **Das war's!**

Nach diesen Schritten hast du:
- ✅ Code auf GitHub (kostenlos)
- ✅ Live Website im Internet (kostenlos)
- ✅ Eigene URL zum Teilen
- ✅ Automatische Updates möglich

**Welche Hosting-Option gefällt dir am besten?**
