# Website Cloner

A simple Python web application that allows you to clone websites by downloading their HTML content.

## Features

- 🌐 **Simple Interface**: Just paste a URL and click clone
- 📥 **Download HTML**: Downloads the complete HTML source code
- 📁 **File Management**: View and download all cloned files
- 🔒 **Safe Filenames**: Automatically creates safe filenames with timestamps
- ⚡ **Fast**: Quick downloading with proper error handling

## How to Use

1. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Run the Application**:
   ```bash
   python app.py
   ```

3. **Open Your Browser**:
   - Go to `http://localhost:5000`

4. **Clone a Website**:
   - Paste any URL (e.g., `https://example.com` or just `example.com`)
   - Click "Clone Website"
   - Wait for the download to complete
   - Download the HTML file

## How It Works

1. **Input**: You provide a website URL
2. **Fetch**: The app sends an HTTP request to get the HTML content
3. **Save**: The HTML is saved with a timestamp in the `downloads/` folder
4. **Download**: You can download the saved HTML file

## File Structure

```
packet web sender/
├── app.py              # Main Flask application
├── templates/
│   └── index.html      # Web interface
├── downloads/          # Downloaded HTML files (created automatically)
├── requirements.txt    # Python dependencies
└── README.md          # This file
```

## Features Explained

- **Auto Protocol**: Adds `https://` if you don't include it
- **Browser Headers**: Uses real browser headers to avoid blocking
- **Error Handling**: Shows clear error messages if something goes wrong
- **File List**: See all your downloaded files with sizes and dates
- **Safe Names**: Converts URLs to safe filenames with timestamps

## Example Usage

1. Enter: `github.com`
2. Click "Clone Website"
3. Get: `github.com_20250724_142530.html`

## Troubleshooting

- **"Failed to download"**: The website might be blocking requests or doesn't exist
- **"Network error"**: Check your internet connection
- **Empty file**: Some websites use JavaScript to load content (this tool only gets static HTML)

## Limitations

- Only downloads HTML content (no CSS, images, or JavaScript files)
- Some websites may block automated requests
- Dynamic content loaded by JavaScript won't be captured
